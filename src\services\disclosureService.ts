// Disclosure Service - Only handles disclosure data fetching
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://127.0.0.1:8000';

// Types for disclosure data
export interface DisclosureItem {
  se_no: number;
  type: string;
  disclosure: string;
}

export interface DisclosureRequest {
  policyId: string;
  customerId: string;
  policyType: string;
}

export interface DisclosureResponse {
  success: boolean;
  data: DisclosureItem[];
  message?: string;
}

/**
 * Fetch disclosure data from backend API
 * @param requestData - The disclosure request parameters
 * @returns Promise with disclosure response
 */
export const fetchDisclosureData = async (
  requestData: DisclosureRequest
): Promise<DisclosureResponse> => {
  try {
    console.log('🔍 Fetching disclosure data from:', `${API_BASE_URL}/disclosures`);
    console.log('📋 Request data:', requestData);

    const url = `${API_BASE_URL}/disclosures`;
    
    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
    });

    console.log('📡 Response status:', response.status);
    console.log('📡 Response headers:', Object.fromEntries(response.headers.entries()));

    if (!response.ok) {
      const errorText = await response.text();
      console.error('❌ API Error Response:', errorText);
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const data = await response.json();
    console.log('✅ Disclosure data received:', data);

    // Validate response structure
    if (!Array.isArray(data)) {
      console.warn('⚠️ Expected array response, got:', typeof data);
      return {
        success: false,
        data: [],
        message: 'Invalid response format from server'
      };
    }

    return {
      success: true,
      data: data,
      message: 'Disclosure data loaded successfully'
    };

  } catch (error) {
    console.error('❌ Error fetching disclosure data:', error);
    
    return {
      success: false,
      data: [],
      message: error instanceof Error ? error.message : 'Failed to fetch disclosure data'
    };
  }
};
