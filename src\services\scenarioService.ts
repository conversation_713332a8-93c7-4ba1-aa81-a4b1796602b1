/**
 * Scenario Service - Backend API Integration
 * 
 * This service handles API calls related to scenario data fetching
 * and backend integration for the scenario details system.
 */

// API Base URL - should match the existing API configuration
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000';

/**
 * Interface for scenario table data row
 */
export interface ScenarioTableRow {
  policyYear: number;
  endOfAge: number;
  plannedPremium: number;
  netOutlay: number;
  netSurrenderValue: number;
  netDeathBenefit: number;
}

/**
 * Interface for the backend response containing scenario details
 */
export interface ScenarioDetailsResponse {
  success: boolean;
  data: ScenarioTableRow[];
  message?: string;
  metadata?: {
    scenarioId: string;
    policyId: string;
    customerId: string;
    category: string;
    totalRows: number;
  };
}

/**
 * Interface for the request payload to get scenario details
 */
export interface ScenarioDetailsRequest {
  scenarioId: string;
  policyId: string;
  customerId: string;
  category?: string;
  parameters?: any; // Additional scenario parameters if needed
}

/**
 * Fetches scenario details from the backend
 *
 * @param requestData - Scenario and policy information
 * @returns Promise<ScenarioDetailsResponse>
 */
export const fetchScenarioDetails = async (
  requestData: ScenarioDetailsRequest
): Promise<ScenarioDetailsResponse> => {
  try {
    // 🚀 BACKEND API CALL - Using GET method for scenario details
    const url = `${API_BASE_URL}/disclosures${requestData.scenarioId}`;
    console.log('🔍 Making API call to:', url);
    console.log('📋 Request data:', requestData);

    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        // Add any authentication headers if needed
        // 'Authorization': `Bearer ${token}`,
      },
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    console.log('✅ Backend response:', data);

    // Transform backend data to match our interface
    const transformedData = transformBackendData(data);

    return {
      success: true,
      data: transformedData,
      message: 'Scenario details fetched successfully',
      metadata: {
        scenarioId: requestData.scenarioId,
        policyId: requestData.policyId,
        customerId: requestData.customerId,
        category: requestData.category || 'unknown',
        totalRows: transformedData.length
      }
    };

  } catch (error: any) {
    console.error('❌ Error fetching scenario details from backend:', error);
    
    // Return failure status with empty data
    return {
      success: false,
      data: [],
      message: `Backend API call failed: ${error.message}`,
      metadata: {
        scenarioId: requestData.scenarioId,
        policyId: requestData.policyId,
        customerId: requestData.customerId,
        category: requestData.category || 'unknown',
        totalRows: 0
      }
    };
  }
};

/**
 * Helper function to transform backend data to our expected format
 * Adjust this based on your actual backend response structure
 */
const transformBackendData = (backendData: any): ScenarioTableRow[] => {
  try {
    // Handle different possible backend response structures
    let dataArray = backendData;
    
    // If backend returns { data: [...] } structure
    if (backendData.data && Array.isArray(backendData.data)) {
      dataArray = backendData.data;
    }
    
    // If backend returns { results: [...] } structure
    if (backendData.results && Array.isArray(backendData.results)) {
      dataArray = backendData.results;
    }
    
    // If backend returns { scenarios: [...] } structure
    if (backendData.scenarios && Array.isArray(backendData.scenarios)) {
      dataArray = backendData.scenarios;
    }

    if (!Array.isArray(dataArray)) {
      console.warn('⚠️ Backend data is not an array:', backendData);
      return [];
    }

    // Transform each row to match our interface
    return dataArray.map((row: any, index: number) => ({
      policyYear: row.policy_year || row.policyYear || (2025 + index),
      endOfAge: row.end_of_age || row.endOfAge || (40 + index),
      plannedPremium: parseFloat(row.planned_premium || row.plannedPremium || 0),
      netOutlay: parseFloat(row.net_outlay || row.netOutlay || 0),
      netSurrenderValue: parseFloat(row.net_surrender_value || row.netSurrenderValue || 0),
      netDeathBenefit: parseFloat(row.net_death_benefit || row.netDeathBenefit || 0)
    }));

  } catch (error) {
    console.error('❌ Error transforming backend data:', error);
    return [];
  }
};

/**
 * Interface for disclosure data item
 */
export interface DisclosureItem {
  seNo: number;
  type: string;
  disclosure: string;
}

/**
 * Interface for the backend response containing disclosure data
 */
export interface DisclosureResponse {
  success: boolean;
  data: DisclosureItem[];
  message?: string;
}

/**
 * Interface for the request payload to get disclosure data
 */
export interface DisclosureRequest {
  policyId: string;
  customerId: string;
  policyType?: string;
}

/**
 * Fetches disclosure data from the backend
 *
 * @param requestData - Policy and customer information
 * @returns Promise<DisclosureResponse>
 */
export const fetchDisclosureData = async (
  requestData: DisclosureRequest
): Promise<DisclosureResponse> => {
  try {
    // 🚀 BACKEND API CALL - Using GET method for disclosure data
    const url = `${API_BASE_URL}/disclosure-data/${requestData.policyId}`;
    console.log('🔍 Making API call to:', url);
    console.log('📋 Request data:', requestData);

    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        // Add any authentication headers if needed
        // 'Authorization': `Bearer ${token}`,
      },
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    console.log('✅ Backend disclosure response:', data);

    // Transform backend data to match our interface
    const transformedData = transformDisclosureData(data);

    return {
      success: true,
      data: transformedData,
      message: 'Disclosure data fetched successfully'
    };

  } catch (error: any) {
    console.error('❌ Error fetching disclosure data from backend:', error);

    // Return failure status with empty data
    return {
      success: false,
      data: [],
      message: `Backend API call failed: ${error.message}`
    };
  }
};

/**
 * Helper function to transform backend disclosure data to our expected format
 */
const transformDisclosureData = (backendData: any): DisclosureItem[] => {
  try {
    // Handle different possible backend response structures
    let dataArray = backendData;

    // If backend returns { data: [...] } structure
    if (backendData.data && Array.isArray(backendData.data)) {
      dataArray = backendData.data;
    }

    // If backend returns { disclosures: [...] } structure
    if (backendData.disclosures && Array.isArray(backendData.disclosures)) {
      dataArray = backendData.disclosures;
    }

    // If backend returns { results: [...] } structure
    if (backendData.results && Array.isArray(backendData.results)) {
      dataArray = backendData.results;
    }

    if (!Array.isArray(dataArray)) {
      console.warn('⚠️ Backend disclosure data is not an array:', backendData);
      return [];
    }

    // Transform each row to match our interface
    return dataArray.map((item: any, index: number) => ({
      seNo: item.se_no || item.seNo || item.serial_number || (index + 1),
      type: item.type || item.disclosure_type || item.title || 'Unknown Type',
      disclosure: item.disclosure || item.description || item.content || item.text || 'No disclosure text available'
    }));

  } catch (error) {
    console.error('❌ Error transforming backend disclosure data:', error);
    return [];
  }
};

/**
 * Alternative endpoint for fetching scenario details with POST method
 * Use this if your backend requires POST with request body
 */
export const fetchScenarioDetailsPost = async (
  requestData: ScenarioDetailsRequest
): Promise<ScenarioDetailsResponse> => {
  try {
    const url = `${API_BASE_URL}/scenario-details`;
    console.log('🔍 Making POST API call to:', url);
    console.log('📋 Request data:', requestData);

    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        // Add any authentication headers if needed
        // 'Authorization': `Bearer ${token}`,
      },
      body: JSON.stringify(requestData)
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    console.log('✅ Backend response:', data);

    const transformedData = transformBackendData(data);

    return {
      success: true,
      data: transformedData,
      message: 'Scenario details fetched successfully',
      metadata: {
        scenarioId: requestData.scenarioId,
        policyId: requestData.policyId,
        customerId: requestData.customerId,
        category: requestData.category || 'unknown',
        totalRows: transformedData.length
      }
    };

  } catch (error: any) {
    console.error('❌ Error fetching scenario details from backend:', error);

    return {
      success: false,
      data: [],
      message: `Backend API call failed: ${error.message}`,
      metadata: {
        scenarioId: requestData.scenarioId,
        policyId: requestData.policyId,
        customerId: requestData.customerId,
        category: requestData.category || 'unknown',
        totalRows: 0
      }
    };
  }
};
