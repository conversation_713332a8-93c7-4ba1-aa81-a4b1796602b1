from fastapi import HTTPException
from app.db.connection import get_connection
from app.models.get_illustration_scenario_models import (
    GetIllustrationScenarioResponse,
    IllustrationObject,
    IllustrationTypeOut,
    IllustrationQuestionOut,
    IllustrationOptionOut
)

def get_illustration_scenario_service(policy_id: int, scenario_id: int) -> GetIllustrationScenarioResponse:
    connection = get_connection()
    cursor = connection.cursor(dictionary=True)

    try:
        # Step 1: Fetch scenario data
        cursor.execute("""
            SELECT * FROM ILLUSTRATION_SCENARIO_TABLE
            WHERE POLICY_ID = %s AND SCENARIO_ID = %s
        """, (policy_id, scenario_id))

        scenario_row = cursor.fetchone()
        if not scenario_row:
            raise HTTPException(status_code=404, detail="No scenario found")

        # Extract IDs
        type_id = scenario_row["ILLUSTRATION_TYPE_ID"]
        question_id = scenario_row["ILLUSTRATION_QUESTION_ID"]
        option_id = scenario_row["ILLUSTRATION_OPTION_ID"]

        # Step 2: Fetch type description
        cursor.execute("""
            SELECT SHORT_DESCRIPTION FROM ILLUSTRATION_TYPE_TABLE
            WHERE ILLUSTRATION_TYPE_ID = %s
        """, (type_id,))
        type_description = cursor.fetchone()
        if not type_description:
            type_description = {"SHORT_DESCRIPTION": ""}

        # Step 3: Fetch question description
        cursor.execute("""
            SELECT SHORT_DESCRIPTION FROM ILLUSTRATION_QUESTION_TABLE
            WHERE ILLUSTRATION_QUESTION_ID = %s
        """, (question_id,))
        question_description = cursor.fetchone()
        if not question_description:
            question_description = {"SHORT_DESCRIPTION": ""}

        # Step 4: Fetch option description
        cursor.execute("""
            SELECT SHORT_DESCRIPTION FROM ILLUSTRATION_OPTION_TABLE
            WHERE ILLUSTRATION_OPTION_ID = %s
        """, (option_id,))
        option_description = cursor.fetchone()
        if not option_description:
            option_description = {"SHORT_DESCRIPTION": ""}

        # Step 5: Prepare the nested structure
        option_obj = IllustrationOptionOut(
            option_id=option_id,
            option_description=option_description["SHORT_DESCRIPTION"]
        )

        question_obj = IllustrationQuestionOut(
            question_id=question_id,
            question_description=question_description["SHORT_DESCRIPTION"],
            options=[option_obj]
        )

        type_obj = IllustrationTypeOut(
            type_id=type_id,
            type_description=type_description["SHORT_DESCRIPTION"],
            questions=[question_obj]
        )

        illustration_object = IllustrationObject(
            policy_id=policy_id,
            scenario_id=scenario_id,
            illustration_options=[type_obj]
        )

        # Step 6: Build final response
        return GetIllustrationScenarioResponse(
            illustration=[illustration_object],
            policy_id=policy_id,
            scenario_id=scenario_id,
            illustration_id=scenario_row["ILLUSTRATION_ID"],
            date_of_illustration=scenario_row["DATE_OF_ILLUSTRATION"],
            illustration_type_id=scenario_row["ILLUSTRATION_TYPE_ID"],
            illustration_question_id=scenario_row["ILLUSTRATION_QUESTION_ID"],
            illustration_option_id=scenario_row["ILLUSTRATION_OPTION_ID"],
            illustration_starting_age=scenario_row["ILLUSTRATION_STARTING_AGE"],
            illustration_ending_age=scenario_row["ILLUSTRATION_ENDING_AGE"],
            new_face_amount=scenario_row["NEW_FACE_AMOUNT"],
            new_coverage_option=scenario_row["NEW_COVERAGE_OPTION"],
            new_premium_amount=scenario_row["NEW_PREMIUM_AMOUNT"],
            new_loan_amount=scenario_row["NEW_LOAN_AMOUNT"],
            new_loan_repayment_amount=scenario_row["NEW_LOAN_REPAYMENT_AMOUNT"],
            current_interest_rate=scenario_row["CURRENT_INTEREST_RATE"],
            guaranteed_minimum_rate=scenario_row["GUARANTEED_INTEREST_RATE"],
            illustration_interest_rate=scenario_row["ILLUSTRATION_INTEREST_RATE"],
            surrrender_amount=scenario_row["SURRENDER_AMOUNT"],
            is_schedule=scenario_row["SCHEDULE"]
        )

    finally:
        cursor.close()
        connection.close()

#services.py
def fetch_all_disclosures() -> list:
    try:
        conn = get_connection()
        cursor = conn.cursor(dictionary=True)

        cursor.execute("""
            SELECT DISCLOSURE_ID, TYPE, DISCLOSURE
            FROM INFORCE_ILLUSTRATION_DISCLOSURE
            ORDER BY DISCLOSURE_ID
        """)
        disclosures = cursor.fetchall()

        if not disclosures:
            raise HTTPException(status_code=404, detail="No disclosures found.")

        return disclosures

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error fetching disclosures: {str(e)}")